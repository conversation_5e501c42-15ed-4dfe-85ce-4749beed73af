{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396178680799005", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396178680933023", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396178681017576", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 23964}, "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396178681971401", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 21025}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396178880008441", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["LAAAACUAAABodHRwczovL3NjdGF1ZGl0aW5nLXBhLmdvb2dsZWFwaXMuY29tAAAA", false, 0], "server": "https://sctauditing-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 27405}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 23251}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 19909}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 21851}, "server": "https://fonts.gstatic.com"}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://api-m.sandbox.paypal.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://api.sandbox.paypal.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://paypalobjects.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://static.cloudflareinsights.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://c.sandbox.paypal.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://www.datadoghq-browser-agent.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://c6.paypal.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://c.paypal.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://t.paypal.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://postcollector.paypal.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://pics.paypal.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://www.paypalobjects.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://ddbm2.paypal.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "server": "https://www.sandbox.paypal.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3BheXBhbC5jb20AAA==", false, 0], "server": "https://c6.paypal.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3BheXBhbC5jb20AAA==", false, 0], "server": "https://pics.paypal.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3BheXBhbC5jb20AAA==", false, 0], "server": "https://www.paypalobjects.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3BheXBhbC5jb20AAA==", false, 0], "server": "https://c.paypal.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3BheXBhbC5jb20AAA==", false, 0], "server": "https://ddbm2.paypal.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396180968149469", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "network_stats": {"srtt": 20336}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396180968596253", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "network_stats": {"srtt": 22017}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396180968828233", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 23652}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396180971124466", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", true, 0], "network_stats": {"srtt": 25204}, "server": "https://www.recaptcha.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396178680160946", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 24321}, "server": "https://www.google.com"}, {"anonymization": ["GAAAABIAAABodHRwczovL3BheXBhbC5jb20AAA==", false, 0], "server": "https://postcollector.paypal.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3BheXBhbC5jb20AAA==", false, 0], "server": "https://t.paypal.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3BheXBhbC5jb20AAA==", false, 0], "server": "https://www.sandbox.paypal.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", true, 0], "network_stats": {"srtt": 26652}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3BheXBhbC5jb20AAA==", false, 0], "network_stats": {"srtt": 20643}, "server": "https://browser-intake-us5-datadoghq.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 23663}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 20271}, "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181007773970", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 25074}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181007700434", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 20664}, "server": "https://googleads.g.doubleclick.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181009200348", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 20303}, "server": "https://i.ytimg.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396180984283315", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "network_stats": {"srtt": 20420}, "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181009694500", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 21451}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181008593588", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 7139}, "server": "https://rr1---sn-j5hbnh-2oie.googlevideo.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181008881196", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 23598}, "server": "https://static.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181008132815", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 24726}, "server": "https://www.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181008228854", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 20631}, "server": "https://www.google.com.kh", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181008633202", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 24428}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181010788825", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 20900}, "server": "https://www.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181009179865", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 25628}, "server": "https://yt3.ggpht.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396181007933511", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 25628}, "server": "https://yt3.googleusercontent.com", "supports_spdy": true}], "supports_quic": {"address": "*************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}