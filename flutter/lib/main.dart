import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:my_first_app/authentications/login.dart';
import 'package:my_first_app/models/cart_model.dart';
import 'package:my_first_app/models/user_provider.dart';
import 'package:my_first_app/screens/cart_screen.dart';
import 'package:provider/provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env");
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => CartModel()),
        ChangeNotifierProvider(create: (context) => UserProvider()),
      ],
      child: const ECommerceApp(),
    ),
  );
}

class ECommerceApp extends StatelessWidget {
  const ECommerceApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Explore',
      theme: ThemeData(primarySwatch: Colors.orange, fontFamily: 'Poppins'),
      home: const LoginApp(),
      routes: {
        '/cart': (context) => const CartScreen(),
      },
    );
  }
}
