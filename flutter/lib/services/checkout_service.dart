import 'package:flutter/material.dart';
import '../models/cart_model.dart';
import '../models/order_model.dart';
import 'api_service.dart';

class CheckoutService {
  static final ApiService _apiService = ApiService();

  /// Process checkout with <PERSON><PERSON> backend
  static Future<Map<String, dynamic>> processCheckout({
    required String customerEmail,
    required String customerName,
    required String shippingAddress,
    required String shippingCity,
    required String shippingState,
    required String shippingZip,
    required CartModel cart,
    double shippingCost = 0.0,
    double taxAmount = 0.0,
    String? notes,
  }) async {
    try {
      // Prepare checkout items from cart
      final List<Map<String, dynamic>> items = cart.items.map((cartItem) {
        return {
          'product_id': cartItem.product.id,
          'quantity': cartItem.quantity,
        };
      }).toList();

      // Create checkout request
      final checkoutRequest = {
        'customer_email': customerEmail,
        'customer_name': customerName,
        'shipping_address': shippingAddress,
        'shipping_city': shippingCity,
        'shipping_state': shippingState,
        'shipping_zip': shippingZip,
        'items': items,
        'shipping_cost': shippingCost,
        'tax_amount': taxAmount,
        'notes': notes,
        'payment_method': 'credit_card', // Default payment method
      };

      // Send request to <PERSON>vel backend
      final response = await _apiService.processCheckout(checkoutRequest);

      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Checkout failed',
        'error': e.toString(),
      };
    }
  }

  /// Get checkout summary
  static Future<OrderModel?> getCheckoutSummary(int orderId) async {
    try {
      final response = await _apiService.getCheckoutSummary(orderId);
      
      if (response['success'] == true && response['data'] != null) {
        final orderData = response['data']['order'];
        return OrderModel.fromJson(orderData);
      }
      return null;
    } catch (e) {
      print('Error getting checkout summary: $e');
      return null;
    }
  }

  /// Get orders list
  static Future<List<OrderModel>> getOrders({int page = 1}) async {
    try {
      final response = await _apiService.getOrders(page: page);
      
      if (response['success'] == true && response['data'] != null) {
        final ordersData = response['data']['data'] as List;
        return ordersData.map((orderJson) => OrderModel.fromJson(orderJson)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting orders: $e');
      return [];
    }
  }

  /// Get specific order
  static Future<OrderModel?> getOrder(int orderId) async {
    try {
      final response = await _apiService.getOrder(orderId);
      
      if (response['success'] == true && response['data'] != null) {
        return OrderModel.fromJson(response['data']);
      }
      return null;
    } catch (e) {
      print('Error getting order: $e');
      return null;
    }
  }

  /// Show checkout dialog with form
  static Future<Map<String, dynamic>?> showCheckoutDialog({
    required BuildContext context,
    required CartModel cart,
    double shippingCost = 10.0,
    double taxRate = 0.08, // 8% tax
  }) async {
    final formKey = GlobalKey<FormState>();
    final emailController = TextEditingController();
    final nameController = TextEditingController();
    final addressController = TextEditingController();
    final cityController = TextEditingController();
    final stateController = TextEditingController();
    final zipController = TextEditingController();

    // Calculate tax amount
    final taxAmount = cart.totalPrice * taxRate;

    return showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Checkout'),
          content: SingleChildScrollView(
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Order Summary
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Order Summary', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        Text('Items: \$${cart.totalPrice.toStringAsFixed(2)}'),
                        Text('Shipping: \$${shippingCost.toStringAsFixed(2)}'),
                        Text('Tax: \$${taxAmount.toStringAsFixed(2)}'),
                        const Divider(),
                        Text(
                          'Total: \$${(cart.totalPrice + shippingCost + taxAmount).toStringAsFixed(2)}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Customer Information
                  TextFormField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your email';
                      }
                      if (!value.contains('@')) {
                        return 'Please enter a valid email';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                  
                  TextFormField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Full Name',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                  
                  TextFormField(
                    controller: addressController,
                    decoration: const InputDecoration(
                      labelText: 'Address',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your address';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                  
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: cityController,
                          decoration: const InputDecoration(
                            labelText: 'City',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          controller: stateController,
                          decoration: const InputDecoration(
                            labelText: 'State',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  
                  TextFormField(
                    controller: zipController,
                    decoration: const InputDecoration(
                      labelText: 'ZIP Code',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter ZIP code';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(null);
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  // Show loading
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );

                  // Process checkout
                  final result = await processCheckout(
                    customerEmail: emailController.text,
                    customerName: nameController.text,
                    shippingAddress: addressController.text,
                    shippingCity: cityController.text,
                    shippingState: stateController.text,
                    shippingZip: zipController.text,
                    cart: cart,
                    shippingCost: shippingCost,
                    taxAmount: taxAmount,
                  );

                  // Hide loading
                  Navigator.of(context).pop();
                  
                  // Return result
                  Navigator.of(context).pop(result);
                }
              },
              child: const Text('Place Order'),
            ),
          ],
        );
      },
    );
  }
}
