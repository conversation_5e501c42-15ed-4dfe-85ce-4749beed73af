
class AppConfig {
  // Server configuration for local Laravel
  static String apiHost = '127.0.0.1:8000'; // Include port in host
  static int apiPort = 80;

  // API endpoint
  static String get apiBaseUrl => 'http://$apiHost/api';

  // App settings
  static const String appName = 'Store';
  static const String appVersion = '1.0.0';

  // Timeout settings
  static const int connectionTimeoutSeconds = 60;
  static const int receiveTimeoutSeconds = 90;

  // Retry settings
  static const int maxRetryAttempts = 3;
  static const int retryDelaySeconds = 3;

  // Currency
  static const String currency = 'USD';
}
