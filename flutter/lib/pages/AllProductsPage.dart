import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import '../components/ProductCard.dart';
import '../controller/product_controller.dart';
import '../models/cart_model.dart';
import '../models/category_model.dart';
import '../screens/cart_screen.dart';
import 'dart:math' as math;

class AllProductsPage extends StatefulWidget {
  const AllProductsPage({super.key});

  @override
  State<AllProductsPage> createState() => _AllProductsPageState();
}

class _AllProductsPageState extends State<AllProductsPage> with SingleTickerProviderStateMixin {
  // Use Get.find() instead of Get.put() to avoid creating multiple instances
  late final ProductController productController;
  RxInt selectedCategoryId = RxInt(-1); // -1 means all products

  // For sorting options
  final RxString _selectedSortOption = 'Newest'.obs;
  final List<String> _sortOptions = ['Newest', 'Price: Low to High', 'Price: High to Low', 'Popularity'];

  // Animation controller for the filter button
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    // Initialize the controller
    productController = Get.isRegistered<ProductController>()
        ? Get.find<ProductController>()
        : Get.put(ProductController());

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Use a post-frame callback to load products after the build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAllProducts();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Load all products and store them
  void _loadAllProducts() {
    // This will now be called after the initial build is complete
    productController.getProduct();
  }

  // Sort products based on selected option
  void _sortProducts(String option) {
    _selectedSortOption.value = option;

    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sorting by $option - Coming soon!'),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFDF6F0),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Products',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        // Removed the back button and cart button to avoid duplication
        automaticallyImplyLeading: false,
      ),
      body: CustomScrollView(
        slivers: [
          // Search Bar with Sort/Filter
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: _buildSearchBar(),
            ),
          ),

          // Category Selector
          SliverToBoxAdapter(
            child: _buildCategorySection(),
          ),

          // Title and Filter Status
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
              child: Obx(() {
                String title = "All Products";
                if (selectedCategoryId.value != -1) {
                  // Find the category name
                  final category = productController.categories.firstWhere(
                    (cat) => cat.id == selectedCategoryId.value,
                    orElse: () => CategoryModel(id: -1, name: "Unknown"),
                  );
                  // Shorten the title if it's "Home & Kitchen" to avoid overflow
                  if (category.name == "Home & Kitchen") {
                    title = "Home & Kitchen";
                  } else {
                    title = "${category.name} Products";
                  }
                }

                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (productController.loading.value)
                            const Text(
                              'Loading...',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            )
                          else
                            Text(
                              '${productController.listProduct.length} products',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (selectedCategoryId.value != -1)
                            TextButton.icon(
                              onPressed: () {
                                // Update the UI immediately
                                selectedCategoryId.value = -1;
                                productController.getProduct();

                                // Force a UI refresh
                                setState(() {});
                              },
                              icon: const Icon(Icons.clear, size: 16),
                              label: const Text("Clear"),
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.orange,
                                padding: const EdgeInsets.symmetric(horizontal: 4),
                              ),
                            ),
                          // Sort dropdown
                          PopupMenuButton<String>(
                            icon: const Icon(Icons.sort, color: Colors.orange),
                            onSelected: (String value) {
                              _sortProducts(value);
                            },
                            itemBuilder: (BuildContext context) {
                              return _sortOptions.map((String option) {
                                return PopupMenuItem<String>(
                                  value: option,
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.check,
                                        color: _selectedSortOption.value == option
                                            ? Colors.orange
                                            : Colors.transparent,
                                        size: 18,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(option),
                                    ],
                                  ),
                                );
                              }).toList();
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }),
            ),
          ),

          // Products Grid
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: Obx(() {
              if (productController.loading.value) {
                return SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Loading products...',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              if (productController.errorMessage.value.isNotEmpty) {
                return SliverFillRemaining(
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      margin: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.red[200]!),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.error_outline, color: Colors.red, size: 48),
                          const SizedBox(height: 16),
                          Text(
                            'Error Loading Products',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.red[800],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            productController.errorMessage.value,
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.red[700]),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: () {
                              productController.getProduct();
                            },
                            icon: const Icon(Icons.refresh),
                            label: const Text('Try Again'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red[400],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              final products = productController.listProduct;
              if (products.isEmpty) {
                return SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.inventory_2_outlined, size: 80, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'No products found',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Try selecting a different category',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 24),
                        if (selectedCategoryId.value != -1)
                          ElevatedButton.icon(
                            onPressed: () {
                              selectedCategoryId.value = -1;
                              productController.getProduct();
                              setState(() {});
                            },
                            icon: const Icon(Icons.refresh),
                            label: const Text('Show All Products'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              }

              return SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12.0,
                  mainAxisSpacing: 12.0,
                  childAspectRatio: 0.58,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final product = products[index];
                    return ProductCard(
                      name: product.name,
                      price: '\$${product.price}',
                      imageUrl: product.imageUrl,
                      product: product,
                      onAddToCart: () {
                        Provider.of<CartModel>(context, listen: false).addItem(product);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${product.name} added to cart!'),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            action: SnackBarAction(
                              label: 'VIEW CART',
                              onPressed: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => const CartScreen(),
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      },
                    );
                  },
                  childCount: products.length,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 55,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.search_rounded,
            color: Colors.grey[600],
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search for products...',
                hintStyle: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 15,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
              ),
              style: TextStyle(
                color: Colors.grey[800],
                fontSize: 15,
              ),
            ),
          ),
          Container(
            width: 1,
            height: 24,
            color: Colors.grey[300],
            margin: const EdgeInsets.symmetric(horizontal: 8),
          ),
          // Animated filter button
          GestureDetector(
            onTap: () {
              // Toggle animation
              if (_animationController.status == AnimationStatus.completed) {
                _animationController.reverse();
              } else {
                _animationController.forward();
              }

              // Show filter options
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Advanced filtering coming soon!'),
                  duration: Duration(seconds: 1),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _animationController.value * math.pi / 2,
                  child: Icon(
                    Icons.tune_rounded,
                    color: Colors.grey[600],
                    size: 22,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection() {
    // Map category names to icons
    Map<String, IconData> categoryIcons = {
      'Electronics': Icons.electrical_services,
      'Laptop': Icons.computer,
      'Kitchen': Icons.home,
      'Home & Kitchen': Icons.kitchen, // Specific icon for Home & Kitchen
      'Sports': Icons.sports_basketball,
      'Clothing': Icons.checkroom,
      'Books': Icons.menu_book_rounded,
      'Furniture': Icons.chair,
      'Beauty': Icons.face_retouching_natural,
      'Toys': Icons.toys,
      'Food': Icons.fastfood,
    };

    return Obx(() {
      if (productController.loadingCategories.value) {
        return Container(
          height: 120,
          alignment: Alignment.center,
          child: const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
          ),
        );
      }

      if (productController.categories.isEmpty) {
        return const SizedBox.shrink(); // Don't show category section if no categories
      }

      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Categories',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // Scroll to the start of the category list
                      // This is just a placeholder for now
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.orange,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                    child: const Text('View All'),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 120, // Increased height to accommodate multi-line text
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: productController.categories.length + 1, // +1 for "All" option
                itemBuilder: (context, index) {
                  // First item is "All Products"
                  if (index == 0) {
                    return _buildCategoryItem(
                      "All",
                      Icons.apps_rounded,
                      -1,
                      selectedCategoryId.value == -1,
                    );
                  }

                  // Adjust index for actual categories
                  final category = productController.categories[index - 1];
                  // Get icon based on category name, or use a default icon
                  final IconData icon = categoryIcons[category.name] ?? Icons.category;

                  return _buildCategoryItem(
                    category.name,
                    icon,
                    category.id,
                    selectedCategoryId.value == category.id,
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }

  // Individual category item
  Widget _buildCategoryItem(String name, IconData icon, int categoryId, bool isSelected) {
    // Adjust width for Home & Kitchen to prevent overflow
    final double containerWidth = (name == "Home & Kitchen") ? 85 : 75;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(right: 12),
      width: containerWidth, // Set explicit width based on category name
      child: GestureDetector(
        onTap: () {
          // First update the UI to show the selected category
          selectedCategoryId.value = categoryId;

          // Then fetch products for this category
          if (categoryId == -1) {
            // Show all products
            productController.getProduct();
          } else {
            // Show filtered products
            productController.getProductsByCategory(categoryId);
          }

          // Force a UI refresh
          setState(() {});
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 65,
              height: 65,
              decoration: BoxDecoration(
                color: isSelected ? Colors.orange : Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: isSelected
                        ? Colors.orange.withAlpha(60)
                        : Colors.black.withAlpha(10),
                    blurRadius: 8,
                    spreadRadius: 1,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: isSelected ? Colors.white : Colors.orange,
                size: 28,
              ),
            ),
            const SizedBox(height: 10),
            Container(
              width: containerWidth, // Match parent width
              height: 32, // Fixed height for text container
              alignment: Alignment.center, // Center the text
              child: Text(
                // For Home & Kitchen, use a line break to avoid overflow
                name == "Home & Kitchen" ? "Home &\nKitchen" : name,
                textAlign: TextAlign.center,
                overflow: TextOverflow.visible, // Allow text to be fully visible
                maxLines: 2,
                style: TextStyle(
                  fontSize: 11,
                  height: 1.1, // Even tighter line height
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? Colors.orange : Colors.grey[700],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
