class OrderModel {
  final int id;
  final String orderNumber;
  final int? customerId;
  final String customerEmail;
  final String customerName;
  final String shippingAddress;
  final String shippingCity;
  final String shippingState;
  final String shippingZip;
  final String shippingCountry;
  final double subtotal;
  final double shippingCost;
  final double taxAmount;
  final double totalAmount;
  final String status;
  final String paymentStatus;
  final String? paymentMethod;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<OrderItemModel>? orderItems;
  final List<PaymentModel>? payments;

  OrderModel({
    required this.id,
    required this.orderNumber,
    this.customerId,
    required this.customerEmail,
    required this.customerName,
    required this.shippingAddress,
    required this.shippingCity,
    required this.shippingState,
    required this.shippingZip,
    required this.shippingCountry,
    required this.subtotal,
    required this.shippingCost,
    required this.taxAmount,
    required this.totalAmount,
    required this.status,
    required this.paymentStatus,
    this.paymentMethod,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.orderItems,
    this.payments,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'],
      orderNumber: json['order_number'],
      customerId: json['customer_id'],
      customerEmail: json['customer_email'],
      customerName: json['customer_name'],
      shippingAddress: json['shipping_address'],
      shippingCity: json['shipping_city'],
      shippingState: json['shipping_state'],
      shippingZip: json['shipping_zip'],
      shippingCountry: json['shipping_country'],
      subtotal: double.parse(json['subtotal'].toString()),
      shippingCost: double.parse(json['shipping_cost'].toString()),
      taxAmount: double.parse(json['tax_amount'].toString()),
      totalAmount: double.parse(json['total_amount'].toString()),
      status: json['status'],
      paymentStatus: json['payment_status'],
      paymentMethod: json['payment_method'],
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      orderItems: json['order_items'] != null
          ? (json['order_items'] as List)
              .map((item) => OrderItemModel.fromJson(item))
              .toList()
          : null,
      payments: json['payments'] != null
          ? (json['payments'] as List)
              .map((payment) => PaymentModel.fromJson(payment))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'customer_id': customerId,
      'customer_email': customerEmail,
      'customer_name': customerName,
      'shipping_address': shippingAddress,
      'shipping_city': shippingCity,
      'shipping_state': shippingState,
      'shipping_zip': shippingZip,
      'shipping_country': shippingCountry,
      'subtotal': subtotal,
      'shipping_cost': shippingCost,
      'tax_amount': taxAmount,
      'total_amount': totalAmount,
      'status': status,
      'payment_status': paymentStatus,
      'payment_method': paymentMethod,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      if (orderItems != null)
        'order_items': orderItems!.map((item) => item.toJson()).toList(),
      if (payments != null)
        'payments': payments!.map((payment) => payment.toJson()).toList(),
    };
  }

  String get fullShippingAddress {
    return '$shippingAddress, $shippingCity, $shippingState $shippingZip, $shippingCountry';
  }

  bool get isPaid {
    return paymentStatus == 'paid';
  }

  bool get isPending {
    return status == 'pending';
  }

  bool get isProcessing {
    return status == 'processing';
  }

  bool get isShipped {
    return status == 'shipped';
  }

  bool get isDelivered {
    return status == 'delivered';
  }

  bool get isCancelled {
    return status == 'cancelled';
  }
}

class OrderItemModel {
  final int id;
  final int orderId;
  final int? productId;
  final String productName;
  final String? productImage;
  final double productPrice;
  final int quantity;
  final double totalPrice;
  final DateTime createdAt;
  final DateTime updatedAt;

  OrderItemModel({
    required this.id,
    required this.orderId,
    this.productId,
    required this.productName,
    this.productImage,
    required this.productPrice,
    required this.quantity,
    required this.totalPrice,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OrderItemModel.fromJson(Map<String, dynamic> json) {
    return OrderItemModel(
      id: json['id'],
      orderId: json['order_id'],
      productId: json['product_id'],
      productName: json['product_name'],
      productImage: json['product_image'],
      productPrice: double.parse(json['product_price'].toString()),
      quantity: json['quantity'],
      totalPrice: double.parse(json['total_price'].toString()),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'product_id': productId,
      'product_name': productName,
      'product_image': productImage,
      'product_price': productPrice,
      'quantity': quantity,
      'total_price': totalPrice,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class PaymentModel {
  final int id;
  final int orderId;
  final String paymentId;
  final String paymentMethod;
  final double amount;
  final String currency;
  final String status;
  final DateTime? paidAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentModel({
    required this.id,
    required this.orderId,
    required this.paymentId,
    required this.paymentMethod,
    required this.amount,
    required this.currency,
    required this.status,
    this.paidAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      id: json['id'],
      orderId: json['order_id'],
      paymentId: json['payment_id'],
      paymentMethod: json['payment_method'],
      amount: double.parse(json['amount'].toString()),
      currency: json['currency'],
      status: json['status'],
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'payment_id': paymentId,
      'payment_method': paymentMethod,
      'amount': amount,
      'currency': currency,
      'status': status,
      'paid_at': paidAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isSuccessful {
    return status == 'executed' || status == 'approved';
  }

  bool get isPending {
    return status == 'created';
  }

  bool get isFailed {
    return status == 'failed';
  }

  bool get isCancelled {
    return status == 'cancelled';
  }
}
