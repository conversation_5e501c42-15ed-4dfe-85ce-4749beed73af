import 'dart:io';
import 'package:flutter/material.dart';
import 'package:my_first_app/models/user_model.dart';
import 'package:my_first_app/services/api_service.dart';

class UserProvider extends ChangeNotifier {
  UserModel? _user;
  bool _isLoading = false;
  String _error = '';
  final ApiService _apiService = ApiService();

  UserModel? get user => _user;
  bool get isLoading => _isLoading;
  String get error => _error;

  bool get isLoggedIn => _user != null;

  // Initialize user data
  Future<void> initUser() async {
    if (_user != null) return;
    
    try {
      _isLoading = true;
      notifyListeners();

      final userData = await _apiService.getUserProfile();
      _user = UserModel.fromJson(userData);
      _error = '';
    } catch (e) {
      _error = e.toString();
      _user = UserModel(
        id: 1,
        name: 'Guest User',
        email: '<EMAIL>',
        profileImageUrl: 'https://ui-avatars.com/api/?name=Guest+User&background=FFA500&color=fff',
      );
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update user profile image
  Future<bool> updateProfileImage(File imageFile) async {
    if (_user == null) return false;

    try {
      _isLoading = true;
      notifyListeners();

      final success = await _apiService.uploadProfileImage(_user!.id, imageFile);
      
      if (success) {
        // Refresh the profile image URL with a timestamp to force reload
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final baseApiUrl = 'http://127.0.0.1:8000/api';
        final newImageUrl = '$baseApiUrl/users/${_user!.id}/profile/image?t=$timestamp';
        
        _user = _user!.copyWith(profileImageUrl: newImageUrl);
        _error = '';
        notifyListeners();
        return true;
      } else {
        _error = 'Failed to update profile image';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update user profile data
  void updateUserData({String? name, String? email}) {
    if (_user == null) return;

    _user = _user!.copyWith(
      name: name,
      email: email,
    );
    notifyListeners();
    
   }

  void clearUser() {
    _user = null;
    notifyListeners();
  }
} 