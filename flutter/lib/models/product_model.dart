class ProductModel {
  final int id;
  final String name;
  final String description;
  final double price;
  final String imageUrl;

  ProductModel({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    final int productId = json['id'] ?? 0;
    
    // Construct image URL using the correct products/{id}/images format
    final baseApiUrl = 'http://127.0.0.1:8000/api';
    final String imageUrl = '$baseApiUrl/products/$productId/image';
    
    return ProductModel(
      id: productId,
      name: json['product_name'] ?? json['name'] ?? 'Unknown Product',
      description: json['description'] ?? 'No description available',
      price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
      imageUrl: imageUrl,
    );
  }
}
