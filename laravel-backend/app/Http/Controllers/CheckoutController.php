<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckoutController extends Controller
{
    /**
     * Process checkout
     */
    public function processCheckout(Request $request)
    {
        $request->validate([
            'customer_email' => 'required|email',
            'customer_name' => 'required|string|max:255',
            'shipping_address' => 'required|string',
            'shipping_city' => 'required|string|max:100',
            'shipping_state' => 'required|string|max:100',
            'shipping_zip' => 'required|string|max:20',
            'shipping_country' => 'string|max:2',
            'payment_method' => 'required|string|in:credit_card,upi,cod',
            'items' => 'array|min:1',
            'items.*.product_id' => 'exists:products,id',
            'items.*.quantity' => 'integer|min:1',
            'shipping_cost' => 'numeric|min:0',
            'tax_amount' => 'numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Calculate totals from cart items
            $subtotal = 0;
            $orderItems = [];

            if ($request->has('items') && is_array($request->items)) {
                foreach ($request->items as $item) {
                    $product = \App\Models\Product::find($item['product_id']);
                    if (!$product) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Product not found: ' . $item['product_id'],
                        ], 404);
                    }

                    $itemTotal = $product->price * $item['quantity'];
                    $subtotal += $itemTotal;

                    $orderItems[] = [
                        'product_id' => $product->id,
                        'product_name' => $product->product_name,
                        'product_image' => $product->image,
                        'product_price' => $product->price,
                        'quantity' => $item['quantity'],
                        'total_price' => $itemTotal,
                    ];
                }
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart items are required',
                ], 400);
            }

            $shippingCost = $request->shipping_cost ?? 0;
            $taxAmount = $request->tax_amount ?? 0;
            $totalAmount = $subtotal + $shippingCost + $taxAmount;

            // Find or create customer
            $customer = Customer::where('email', $request->customer_email)->first();

            // Create order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'customer_id' => $customer?->id,
                'customer_email' => $request->customer_email,
                'customer_name' => $request->customer_name,
                'shipping_address' => $request->shipping_address,
                'shipping_city' => $request->shipping_city,
                'shipping_state' => $request->shipping_state,
                'shipping_zip' => $request->shipping_zip,
                'shipping_country' => $request->shipping_country ?? 'US',
                'subtotal' => $subtotal,
                'shipping_cost' => $shippingCost,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_method' => $request->payment_method,
                'notes' => $request->notes,
            ]);

            // Create order items
            foreach ($orderItems as $orderItem) {
                $orderItem['order_id'] = $order->id;
                OrderItem::create($orderItem);
            }

            // Create payment record
            \App\Models\Payment::create([
                'order_id' => $order->id,
                'payment_id' => 'PAY-' . uniqid(),
                'payment_method' => $request->payment_method,
                'amount' => $totalAmount,
                'currency' => 'USD',
                'status' => 'pending',
            ]);

            $order->update(['status' => 'confirmed']);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'total_amount' => $totalAmount,
                'payment_method' => $request->payment_method,
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Checkout process failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Checkout failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get checkout summary for an order
     */
    public function getCheckoutSummary($orderId)
    {
        try {
            $order = Order::with(['orderItems.product', 'payments'])
                ->find($orderId);

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'order' => $order,
                    'payment_status' => $order->payment_status,
                    'latest_payment' => $order->payments()->latest()->first(),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Get checkout summary failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to get checkout summary',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
