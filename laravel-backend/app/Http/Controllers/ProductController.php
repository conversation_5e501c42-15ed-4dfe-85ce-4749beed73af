<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    public function index(Request  $request)
    {

        $product = Product::query();
        if ($request->has("id")) {
            $product->where("id", "=", $request->input("id"));
        }
        if ($request->has("categories_id")) {
            $product->where("categories_id", "=", $request->input("categories_id"));
        }
        if ($request->has("brands_id")) {
            $product->where("brands_id", "=", $request->input("brands_id"));
        }
        if ($request->has("text_search")) {
            $product->where("product_name", "LIKE", "%" . $request->input("text_search") . "%");
        }
        if ($request->has("status")) {
            $product->where("status", "=", $request->input("status"));
        }
        $product->with(["categories", "brands"])->paginate();


        $category = DB::select('select * from categories');
        $brand = DB::select('select * from brands');


        return [
            "list" => $product->get(),
            "categories" => $category,
            "brands" => $brand
        ];
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validate request with image URL instead of file upload
        $request->validate([
            'categories_id' => 'required|exists:categories,id',
            'brands_id' => 'required|exists:brands,id',
            'product_name' => 'required|string',
            'description' => 'nullable|string',
            'quantity' => 'required|integer',
            'price' => 'required|numeric',
            'image' => 'nullable|url',
            'status' => 'boolean'
        ]);

        $data = $request->all();
        $product = Product::create($data);

        return response()->json([
            "data" => $product,
            "message" => "Save successfully!"
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $product = Product::find($id);
        return response()->json([
            "data" => $product->load(['categories', 'brands'])
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $product = Product::find($id);
        // Validate request with image URL instead of file upload
        $request->validate([
            'categories_id' => 'required|exists:categories,id',
            'brands_id' => 'required|exists:brands,id',
            'product_name' => 'required|string',
            'description' => 'nullable|string',
            'quantity' => 'required|integer',
            'price' => 'required|numeric',
            'image' => 'nullable|url',
            'status' => 'boolean'
        ]);

        $data = $request->all();

        // Handle image removal
        if ($request->remove_image) {
            $data['image'] = null;
        }

        $product->update($data);
        return response()->json([
            "data" => $product,
            "message" => "Update successfully!"
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $product = Product::findOrFail($id);
            $product->delete();

            return response()->json([
                "success" => true,
                "message" => "Product deleted successfully"
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                "success" => false,
                "message" => "Failed to delete product",
                "error" => $e->getMessage()
            ], 500);
        }
    }
}
