<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Order;

class PayPalCheckoutTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $category = Category::create([
            'name' => 'Test Category',
            'description' => 'Test Description',
            'status' => true
        ]);

        $brand = Brand::create([
            'name' => 'Test Brand',
            'description' => 'Test Description',
            'status' => true
        ]);

        Product::create([
            'categories_id' => $category->id,
            'brands_id' => $brand->id,
            'product_name' => 'Test Product',
            'description' => 'Test Product Description',
            'quantity' => 10,
            'price' => 25.99,
            'status' => true,
            'image' => 'https://example.com/image.jpg'
        ]);
    }

    public function test_checkout_process_creates_order()
    {
        $checkoutData = [
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'shipping_address' => '123 Test St',
            'shipping_city' => 'Test City',
            'shipping_state' => 'TS',
            'shipping_zip' => '12345',
            'shipping_country' => 'US',
            'items' => [
                [
                    'product_id' => 1,
                    'quantity' => 2
                ]
            ],
            'shipping_cost' => 10.00,
            'tax_amount' => 5.00
        ];

        // Mock PayPal service to avoid actual API calls during testing
        $this->app->bind(\App\Services\PayPalService::class, function () {
            $mock = \Mockery::mock(\App\Services\PayPalService::class);
            $mock->shouldReceive('createPayment')
                ->andReturn([
                    'success' => true,
                    'payment' => (object) ['getId' => 'TEST_PAYMENT_ID'],
                    'approval_url' => 'https://sandbox.paypal.com/test'
                ]);
            return $mock;
        });

        $response = $this->postJson('/api/checkout/process', $checkoutData);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Order created and PayPal payment initiated'
                ]);

        // Verify order was created
        $this->assertDatabaseHas('orders', [
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'payment_method' => 'paypal',
            'payment_status' => 'pending'
        ]);

        // Verify order items were created
        $this->assertDatabaseHas('order_items', [
            'product_id' => 1,
            'quantity' => 2,
            'product_name' => 'Test Product'
        ]);
    }

    public function test_checkout_fails_with_insufficient_stock()
    {
        $checkoutData = [
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'shipping_address' => '123 Test St',
            'shipping_city' => 'Test City',
            'shipping_state' => 'TS',
            'shipping_zip' => '12345',
            'items' => [
                [
                    'product_id' => 1,
                    'quantity' => 20 // More than available stock (10)
                ]
            ],
            'shipping_cost' => 10.00,
            'tax_amount' => 5.00
        ];

        $response = $this->postJson('/api/checkout/process', $checkoutData);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Insufficient stock for product: Test Product'
                ]);
    }

    public function test_checkout_validation_fails_with_missing_data()
    {
        $checkoutData = [
            'customer_email' => 'invalid-email',
            // Missing required fields
        ];

        $response = $this->postJson('/api/checkout/process', $checkoutData);

        $response->assertStatus(422); // Validation error
    }

    public function test_get_checkout_summary()
    {
        // Create a test order
        $order = Order::create([
            'order_number' => 'TEST-ORDER-001',
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Customer',
            'shipping_address' => '123 Test St',
            'shipping_city' => 'Test City',
            'shipping_state' => 'TS',
            'shipping_zip' => '12345',
            'shipping_country' => 'US',
            'subtotal' => 51.98,
            'shipping_cost' => 10.00,
            'tax_amount' => 5.00,
            'total_amount' => 66.98,
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => 'paypal'
        ]);

        $response = $this->getJson("/api/checkout/{$order->id}/summary");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'order' => [
                            'id' => $order->id,
                            'order_number' => 'TEST-ORDER-001',
                            'customer_email' => '<EMAIL>'
                        ]
                    ]
                ]);
    }
}
