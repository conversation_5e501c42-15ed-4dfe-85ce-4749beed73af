<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Remove PayPal-specific columns
            $table->dropColumn(['payer_id', 'paypal_response']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Add back PayPal-specific columns
            $table->string('payer_id')->nullable()->after('payment_id');
            $table->json('paypal_response')->nullable()->after('status');
        });
    }
};
