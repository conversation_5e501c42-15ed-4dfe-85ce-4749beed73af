<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-8">
        <div class="max-w-2xl mx-auto px-4">
            <!-- Success Message -->
            <div class="bg-white rounded-lg shadow-sm p-8 text-center">
                <div class="mb-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">Order Confirmed!</h1>
                    <p class="text-gray-600">Thank you for your purchase. Your order has been successfully placed.</p>
                </div>

                <!-- Order Details -->
                <div class="bg-gray-50 rounded-lg p-6 mb-6">
                    <h2 class="text-lg font-semibold mb-4">Order Details</h2>
                    <div class="space-y-2 text-left">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Order Number:</span>
                            <span class="font-medium" id="order-number">{{ request('order') ?? 'ORD-' . time() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Payment Method:</span>
                            <span class="font-medium">PayPal</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Amount:</span>
                            <span class="font-medium">$1,439.99</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status:</span>
                            <span class="text-green-600 font-medium">Confirmed</span>
                        </div>
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="text-left mb-6">
                    <h3 class="font-semibold mb-2">What's Next?</h3>
                    <ul class="text-gray-600 space-y-1">
                        <li>• You will receive an email confirmation shortly</li>
                        <li>• Your order will be processed within 1-2 business days</li>
                        <li>• You'll receive tracking information once shipped</li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="window.location.href='/'" 
                            class="bg-orange-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-600 transition duration-200">
                        Continue Shopping
                    </button>
                    <button onclick="window.print()" 
                            class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition duration-200">
                        Print Receipt
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get order number from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const orderNumber = urlParams.get('order');
        if (orderNumber) {
            document.getElementById('order-number').textContent = orderNumber;
        }
    </script>
</body>
</html>
